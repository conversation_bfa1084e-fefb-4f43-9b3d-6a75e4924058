#!/usr/bin/env python3
"""
Firebase Console Logs Checker

This script provides direct links to Firebase Console logs and debugging information
to help diagnose notification delivery issues.

Usage:
    python3 check_firebase_logs.py
"""

import json
import os
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
except ImportError:
    print("⚠️ python-dotenv not available")

def get_project_info():
    """Get Firebase project information"""
    project_id = None
    
    if os.environ.get('SERVICE_ACCOUNT_JSON'):
        try:
            service_account = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            project_id = service_account.get('project_id')
        except:
            pass
    
    if not project_id:
        project_id = "chat10000-402ac"  # fallback
    
    return project_id

def provide_firebase_console_log_links():
    """Provide direct links to Firebase Console logs"""
    project_id = get_project_info()
    
    print("🔍 FIREBASE CONSOLE LOGS AND DEBUGGING")
    print("=" * 60)
    
    print("1. CLOUD MESSAGING USAGE & LOGS:")
    print(f"   https://console.firebase.google.com/project/{project_id}/messaging/usage")
    print("   • Shows delivery statistics")
    print("   • Error rates and success rates")
    print("   • Recent message activity")
    print()
    
    print("2. CLOUD MESSAGING SETTINGS:")
    print(f"   https://console.firebase.google.com/project/{project_id}/settings/cloudmessaging")
    print("   • APNs authentication status")
    print("   • iOS app configurations")
    print("   • Certificate/key upload status")
    print()
    
    print("3. GOOGLE CLOUD CONSOLE LOGS:")
    print(f"   https://console.cloud.google.com/logs/query?project={project_id}")
    print("   • Detailed FCM delivery logs")
    print("   • APNs connection errors")
    print("   • Authentication failures")
    print()
    
    print("4. FIREBASE ANALYTICS (if enabled):")
    print(f"   https://console.firebase.google.com/project/{project_id}/analytics/events")
    print("   • Notification engagement")
    print("   • Delivery confirmation events")
    print()
    
    print("5. PROJECT OVERVIEW:")
    print(f"   https://console.firebase.google.com/project/{project_id}/overview")
    print("   • General project health")
    print("   • Recent activity")

def provide_log_search_queries():
    """Provide specific log search queries for Google Cloud Console"""
    print("\n🔍 GOOGLE CLOUD CONSOLE LOG QUERIES")
    print("=" * 60)
    
    print("Copy and paste these queries in Google Cloud Console Logs:")
    print()
    
    print("1. FCM DELIVERY ERRORS:")
    print('   resource.type="gce_instance" OR resource.type="cloud_function"')
    print('   "FCM" OR "firebase" OR "messaging"')
    print('   severity>=ERROR')
    print()
    
    print("2. APNs AUTHENTICATION ERRORS:")
    print('   "APNS" OR "apns" OR "Apple Push"')
    print('   "authentication" OR "auth" OR "certificate"')
    print('   severity>=WARNING')
    print()
    
    print("3. NOTIFICATION DELIVERY LOGS:")
    print('   "notification" OR "push"')
    print('   "delivery" OR "send" OR "message"')
    print('   timestamp>="2025-09-04T12:00:00Z"')
    print()
    
    print("4. BUNDLE ID RELATED ERRORS:")
    print('   "me.memorion" OR "bundle" OR "app_id"')
    print('   severity>=WARNING')

def provide_debugging_steps():
    """Provide step-by-step debugging instructions"""
    print("\n🔧 STEP-BY-STEP DEBUGGING")
    print("=" * 60)
    
    print("STEP 1: Check Cloud Messaging Usage")
    print("• Go to Firebase Console > Messaging > Usage")
    print("• Look for recent message attempts")
    print("• Check success/failure rates")
    print("• Note any error patterns")
    print()
    
    print("STEP 2: Verify APNs Configuration")
    print("• Go to Firebase Console > Project Settings > Cloud Messaging")
    print("• Check each iOS app (me.memorion and me.memorion.dev)")
    print("• Verify APNs authentication shows 'Configured'")
    print("• Look for any warning/error messages")
    print()
    
    print("STEP 3: Check Google Cloud Logs")
    print("• Go to Google Cloud Console Logs")
    print("• Use the log queries provided above")
    print("• Filter by last 1-2 hours")
    print("• Look for FCM/APNs related errors")
    print()
    
    print("STEP 4: Test with Firebase Console")
    print("• Go to Firebase Console > Messaging > Send your first message")
    print("• Try sending a test message to a specific token")
    print("• Use one of your FCM tokens from the database")
    print("• Check if it delivers or shows errors")

def provide_common_error_patterns():
    """Provide common error patterns to look for"""
    print("\n🚨 COMMON ERROR PATTERNS TO LOOK FOR")
    print("=" * 60)
    
    print("1. APNs Authentication Errors:")
    print("   • 'APNs certificate expired'")
    print("   • 'Invalid APNs key'")
    print("   • 'Bundle ID mismatch'")
    print("   • 'Team ID mismatch'")
    print()
    
    print("2. Token Registration Errors:")
    print("   • 'Invalid registration token'")
    print("   • 'Token not registered'")
    print("   • 'Sender ID mismatch'")
    print()
    
    print("3. Delivery Errors:")
    print("   • 'APNs delivery failed'")
    print("   • 'Device unreachable'")
    print("   • 'Notification rejected'")
    print()
    
    print("4. Configuration Errors:")
    print("   • 'Project not found'")
    print("   • 'App not configured'")
    print("   • 'Missing permissions'")

def create_test_message_script():
    """Create a script to test with Firebase Console"""
    print("\n📝 FIREBASE CONSOLE TEST MESSAGE")
    print("=" * 60)
    
    print("Use this information to send a test message from Firebase Console:")
    print()
    print("1. Go to Firebase Console > Messaging > Send your first message")
    print("2. Fill in the form:")
    print("   Title: Firebase Console Test")
    print("   Body: Testing from Firebase Console")
    print("3. Click 'Send test message'")
    print("4. Enter one of your FCM tokens:")
    
    # Get a sample token from database
    try:
        sys.path.insert(0, str(Path(__file__).parent))
        from database._client import db
        
        users_ref = db.collection('users')
        for doc in users_ref.stream():
            user_data = doc.to_dict()
            if 'fcm_token' in user_data and user_data['fcm_token']:
                token = user_data['fcm_token']
                print(f"   Sample token: {token}")
                break
    except Exception as e:
        print(f"   (Could not retrieve sample token: {e})")
    
    print("5. Click 'Test' and check if notification is delivered")
    print("6. If it fails, Firebase Console will show the exact error")

def main():
    """Main function"""
    print("=" * 60)
    print("🔍 FIREBASE CONSOLE LOGS CHECKER")
    print("=" * 60)
    print("This script provides links and instructions to check")
    print("Firebase Console logs for notification debugging.")
    print("=" * 60)
    
    provide_firebase_console_log_links()
    provide_log_search_queries()
    provide_debugging_steps()
    provide_common_error_patterns()
    create_test_message_script()
    
    print("\n" + "=" * 60)
    print("🎯 IMMEDIATE ACTION ITEMS:")
    print("1. Open the Firebase Console links above")
    print("2. Check Cloud Messaging Usage for recent activity")
    print("3. Verify APNs configuration status")
    print("4. Use Google Cloud Console to search for error logs")
    print("5. Try sending a test message from Firebase Console")
    print("=" * 60)

if __name__ == "__main__":
    main()
