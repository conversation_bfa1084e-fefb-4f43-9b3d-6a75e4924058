#!/usr/bin/env python3
"""
FCM Token Validation and Cleanup Script

This script validates each FCM token individually against Firebase
and removes invalid tokens that were generated with old Bundle IDs.

Usage:
    cd backend
    python3 validate_and_cleanup_tokens.py
"""

import asyncio
import json
import os
import sys
import logging
from pathlib import Path
from datetime import datetime

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
except ImportError:
    print("⚠️ python-dotenv not available")

import firebase_admin
from firebase_admin import messaging
import database.notifications as notification_db
from database._client import db

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        firebase_admin.get_app()
        print("✓ Firebase already initialized")
        return True
    except ValueError:
        pass
    
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print("✓ Firebase initialized with service account JSON")
            return True
        else:
            firebase_admin.initialize_app()
            print("✓ Firebase initialized with default credentials")
            return True
    except Exception as e:
        print(f"❌ Failed to initialize Firebase: {e}")
        return False

def get_all_user_tokens_with_details():
    """Get all FCM tokens with user details from the database"""
    try:
        print("🔍 Retrieving FCM tokens with user details...")
        users_ref = db.collection('users')
        tokens_data = []
        
        for doc in users_ref.stream():
            user_data = doc.to_dict()
            if 'fcm_token' in user_data and user_data['fcm_token']:
                tokens_data.append({
                    'uid': doc.id,
                    'token': user_data['fcm_token'],
                    'time_zone': user_data.get('time_zone', 'Unknown'),
                    'email': user_data.get('email', 'Unknown'),
                    'created_at': user_data.get('created_at', 'Unknown')
                })
        
        print(f"📱 Found {len(tokens_data)} users with FCM tokens")
        return tokens_data
    
    except Exception as e:
        print(f"❌ Error retrieving FCM tokens: {e}")
        return []

def validate_single_token(token_data):
    """Validate a single FCM token by attempting to send a test message"""
    token = token_data['token']
    uid = token_data['uid']
    
    try:
        # Create a minimal test message (won't actually send due to dry_run=True)
        notification = messaging.Notification(
            title="Token Validation Test",
            body="This is a validation test"
        )
        message = messaging.Message(
            notification=notification,
            token=token
        )
        
        # Use dry_run=True to validate without actually sending
        response = messaging.send(message, dry_run=True)
        return True, "Valid", response
        
    except Exception as e:
        error_msg = str(e).lower()
        
        if "invalid-registration-token" in error_msg or "not a valid fcm registration token" in error_msg:
            return False, "Invalid token format", str(e)
        elif "registration-token-not-registered" in error_msg:
            return False, "Token not registered (Bundle ID mismatch or app uninstalled)", str(e)
        elif "sender-id-mismatch" in error_msg:
            return False, "Sender ID mismatch (Bundle ID changed)", str(e)
        elif "requested entity was not found" in error_msg:
            return False, "Token not found", str(e)
        else:
            return False, f"Other error: {str(e)[:50]}...", str(e)

def validate_all_tokens(tokens_data):
    """Validate all FCM tokens"""
    print(f"\n🔍 VALIDATING {len(tokens_data)} FCM TOKENS")
    print("=" * 60)
    
    valid_tokens = []
    invalid_tokens = []
    
    for i, token_data in enumerate(tokens_data):
        uid = token_data['uid']
        token = token_data['token']
        
        print(f"Testing token {i+1}/{len(tokens_data)} (UID: {uid[:8]}...)")
        
        is_valid, reason, details = validate_single_token(token_data)
        
        if is_valid:
            valid_tokens.append(token_data)
            print(f"  ✅ Valid: {reason}")
        else:
            invalid_tokens.append({
                **token_data,
                'reason': reason,
                'error_details': details
            })
            print(f"  ❌ Invalid: {reason}")
    
    return valid_tokens, invalid_tokens

def cleanup_invalid_tokens(invalid_tokens, dry_run=True):
    """Remove invalid tokens from the database"""
    if not invalid_tokens:
        print("\n✅ No invalid tokens to clean up")
        return
    
    print(f"\n🧹 CLEANING UP {len(invalid_tokens)} INVALID TOKENS")
    print("=" * 60)
    
    if dry_run:
        print("🔍 DRY RUN MODE - No tokens will actually be removed")
        print("Run with --cleanup flag to actually remove tokens")
    
    for token_info in invalid_tokens:
        uid = token_info['uid']
        token = token_info['token']
        reason = token_info['reason']
        
        print(f"{'[DRY RUN] ' if dry_run else ''}Removing token for UID {uid[:8]}... - Reason: {reason}")
        
        if not dry_run:
            try:
                notification_db.remove_token(token)
                print(f"  ✅ Removed token for UID {uid[:8]}...")
            except Exception as e:
                print(f"  ❌ Failed to remove token for UID {uid[:8]}...: {e}")

def provide_ios_debugging_steps():
    """Provide steps to debug iOS token generation"""
    print("\n🔧 iOS TOKEN GENERATION DEBUGGING")
    print("=" * 60)
    
    print("1. CHECK iOS APP LOGS:")
    print("   • Open Xcode > Window > Devices and Simulators")
    print("   • Select your device > Open Console")
    print("   • Filter for 'FCM' or 'Firebase' or 'APNS'")
    print("   • Look for token generation messages")
    print()
    
    print("2. FORCE TOKEN REGENERATION:")
    print("   • Delete app from device")
    print("   • Clear app data/cache")
    print("   • Reinstall app fresh")
    print("   • Check if new token is generated")
    print()
    
    print("3. VERIFY BUNDLE ID IN iOS APP:")
    print("   • Check Xcode project Bundle Identifier")
    print("   • Ensure it matches Firebase project")
    print("   • Verify GoogleService-Info.plist Bundle ID")
    print()
    
    print("4. CHECK FIREBASE CONSOLE:")
    print("   • Verify iOS apps exist for both Bundle IDs")
    print("   • Check APNs authentication is configured")
    print("   • Ensure certificates/keys match Bundle IDs")

def provide_token_regeneration_steps():
    """Provide steps to force token regeneration"""
    print("\n🔄 FORCE TOKEN REGENERATION STEPS")
    print("=" * 60)
    
    print("1. iOS APP SIDE:")
    print("   • Add logging to FCM token generation code")
    print("   • Call FirebaseMessaging.messaging().getToken() explicitly")
    print("   • Check for errors in token generation")
    print("   • Verify token is sent to backend")
    print()
    
    print("2. BACKEND VERIFICATION:")
    print("   • Check if new tokens are being saved")
    print("   • Verify token format matches new Bundle ID")
    print("   • Test new tokens immediately after generation")
    print()
    
    print("3. DEBUGGING COMMANDS:")
    print("   • iOS Simulator: xcrun simctl spawn booted log stream --predicate 'subsystem contains \"com.apple.pushkit\"'")
    print("   • Test individual token: python3 test_single_token.py <token>")
    print("   • Monitor database: Check Firestore for token updates")

def main():
    """Main function"""
    print("=" * 60)
    print("🔍 FCM TOKEN VALIDATION AND CLEANUP")
    print("=" * 60)
    print("This script validates FCM tokens and removes invalid ones")
    print("that were generated with old Bundle IDs.")
    print("=" * 60)
    
    # Check for cleanup flag
    cleanup_mode = '--cleanup' in sys.argv
    
    # Initialize Firebase
    if not initialize_firebase():
        print("❌ Cannot proceed without Firebase initialization")
        sys.exit(1)
    
    # Get all tokens
    tokens_data = get_all_user_tokens_with_details()
    
    if not tokens_data:
        print("❌ No FCM tokens found in database")
        return
    
    # Validate all tokens
    valid_tokens, invalid_tokens = validate_all_tokens(tokens_data)
    
    # Show results
    print(f"\n📊 VALIDATION RESULTS")
    print("=" * 60)
    print(f"Total tokens checked: {len(tokens_data)}")
    print(f"Valid tokens: {len(valid_tokens)}")
    print(f"Invalid tokens: {len(invalid_tokens)}")
    
    if invalid_tokens:
        print(f"\n❌ INVALID TOKENS FOUND:")
        print("-" * 40)
        for token_info in invalid_tokens:
            print(f"UID: {token_info['uid'][:12]}... | Reason: {token_info['reason']}")
    
    # Clean up invalid tokens
    cleanup_invalid_tokens(invalid_tokens, dry_run=not cleanup_mode)
    
    # Provide debugging steps
    if invalid_tokens:
        provide_ios_debugging_steps()
        provide_token_regeneration_steps()
    
    print(f"\n" + "=" * 60)
    if invalid_tokens:
        print("❌ INVALID TOKENS FOUND - iOS app needs to regenerate tokens")
        print("   Follow the debugging steps above to fix token generation")
        if not cleanup_mode:
            print("   Run with --cleanup flag to remove invalid tokens")
    else:
        print("✅ ALL TOKENS ARE VALID - Issue may be elsewhere")
        print("   Check Firebase Console APNs configuration")
    print("=" * 60)

if __name__ == "__main__":
    main()
