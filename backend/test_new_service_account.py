#!/usr/bin/env python3
"""
Test script for new Firebase Admin SDK service account
"""

import json
import os
import firebase_admin
from firebase_admin import credentials, messaging

def test_new_service_account():
    # Replace this with your new Firebase Admin SDK service account JSON
    new_service_account_json = """
    *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    """
    
    try:
        service_account_info = json.loads(new_service_account_json)
        cred = credentials.Certificate(service_account_info)
        firebase_admin.initialize_app(cred)
        
        # Test with your FCM token
        token = "cWixBO7OeEdakAgoUImAXE:APA91bHSeTeQegPJzHxceH5Qpv8GlJlyZ1GCTRW8xYfHHfhLbrDnZ4hBtuxC5hQHU8jV0CVQLKiiV1C8l_DWeqo2IPfZtK199cgAMrSBzw18QJX38hkK000"
        
        message = messaging.Message(
            notification=messaging.Notification(
                title="New Service Account Test",
                body="Testing with Firebase Admin SDK service account"
            ),
            token=token
        )
        
        response = messaging.send(message)
        print(f"✅ Success with new service account: {response}")
        
    except Exception as e:
        print(f"❌ Failed with new service account: {e}")

if __name__ == "__main__":
    test_new_service_account()
