#!/usr/bin/env python3
"""
APNs Environment Checker

This script helps identify APNs environment mismatches that cause
InvalidProviderToken errors.

Usage:
    python3 check_apns_environment.py
"""

import json
import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
except ImportError:
    print("⚠️ python-dotenv not available")

import firebase_admin
from firebase_admin import messaging

def check_apns_environment_mismatch():
    """Check for APNs environment mismatches"""
    print("🔍 CHECKING APNs ENVIRONMENT CONFIGURATION")
    print("=" * 60)
    
    print("ISSUE IDENTIFIED: InvalidProviderToken from APNs")
    print("This typically means:")
    print("• APNs certificate/key is for wrong environment (dev vs prod)")
    print("• APNs certificate/key is expired")
    print("• APNs certificate/key is for wrong Bundle ID")
    print("• APNs certificate/key is corrupted")
    print()
    
    print("FIREBASE CONSOLE vs BACKEND DIFFERENCE:")
    print("• Firebase Console: Uses internal APNs configuration")
    print("• Backend API: Uses uploaded APNs certificates/keys")
    print("• These might be configured for different environments!")

def test_with_explicit_apns_config():
    """Test with explicit APNs configuration"""
    print("\n🧪 TESTING WITH EXPLICIT APNs CONFIGURATION")
    print("=" * 60)
    
    # Get test token
    test_token = "cWixBO7OeEdakAgoUImAXE:APA91bHSeTeQegPJzHxceH5Qpv8GlJlyZ1GCTRW8xYfHHfhLbrDnZ4hBtuxC5hQHU8jV0CVQLKiiV1C8l_DWeqo2IPfZtK199cgAMrSBzw18QJX38hkK000"
    
    test_cases = [
        {
            "name": "Default (no APNs config)",
            "message": messaging.Message(
                notification=messaging.Notification(title="Test Default", body="Testing default"),
                token=test_token
            )
        },
        {
            "name": "Development APNs environment",
            "message": messaging.Message(
                notification=messaging.Notification(title="Test Dev", body="Testing development"),
                apns=messaging.APNSConfig(
                    headers={"apns-push-type": "alert"},
                    payload=messaging.APNSPayload(
                        aps=messaging.Aps(
                            alert=messaging.ApsAlert(title="Test Dev", body="Testing development"),
                            sound="default"
                        )
                    )
                ),
                token=test_token
            )
        },
        {
            "name": "Production APNs environment",
            "message": messaging.Message(
                notification=messaging.Notification(title="Test Prod", body="Testing production"),
                apns=messaging.APNSConfig(
                    headers={
                        "apns-push-type": "alert",
                        "apns-priority": "10"
                    },
                    payload=messaging.APNSPayload(
                        aps=messaging.Aps(
                            alert=messaging.ApsAlert(title="Test Prod", body="Testing production"),
                            sound="default",
                            badge=1
                        )
                    )
                ),
                token=test_token
            )
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        
        try:
            # Test with dry run first
            print(f"   Dry run...")
            dry_response = messaging.send(test_case['message'], dry_run=True)
            print(f"   ✅ Dry run success: {dry_response}")
            
            # Test actual send
            print(f"   Actual send...")
            response = messaging.send(test_case['message'], dry_run=False)
            print(f"   ✅ Actual send success: {response}")
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            
            # Check for specific APNs errors
            error_str = str(e).lower()
            if "invalidprovidertoken" in error_str:
                print(f"   🔍 InvalidProviderToken - APNs auth issue")
            elif "baddevicetoken" in error_str:
                print(f"   🔍 BadDeviceToken - Token issue")
            elif "certificateexpired" in error_str:
                print(f"   🔍 Certificate expired")

def provide_apns_fix_steps():
    """Provide specific steps to fix APNs configuration"""
    print("\n🔧 APNs CONFIGURATION FIX STEPS")
    print("=" * 60)
    
    print("STEP 1: CHECK FIREBASE CONSOLE APNs CONFIGURATION")
    print("1. Go to: https://console.firebase.google.com/project/chat10000-402ac/settings/cloudmessaging")
    print("2. Check iOS apps section")
    print("3. Look for APNs authentication status")
    print("4. Check if certificates/keys are expired")
    print()
    
    print("STEP 2: VERIFY APNs ENVIRONMENT MATCH")
    print("Development Environment:")
    print("• iOS app built with development provisioning profile")
    print("• APNs development certificate/key in Firebase")
    print("• Bundle ID: me.memorion.dev")
    print()
    print("Production Environment:")
    print("• iOS app built with distribution provisioning profile")
    print("• APNs production certificate/key in Firebase")
    print("• Bundle ID: me.memorion")
    print()
    
    print("STEP 3: REGENERATE APNs AUTHENTICATION")
    print("Option A - APNs Key (Recommended):")
    print("1. Go to Apple Developer Console")
    print("2. Create new APNs key (.p8 file)")
    print("3. Upload to Firebase Console")
    print("4. APNs keys work for both dev and prod")
    print()
    print("Option B - APNs Certificates:")
    print("1. Generate separate certificates for dev and prod")
    print("2. Upload correct certificate for each Bundle ID")
    print("3. Ensure environment matches app build type")
    print()
    
    print("STEP 4: TEST ENVIRONMENT MATCHING")
    print("• Development build → Development APNs certificate")
    print("• Production build → Production APNs certificate")
    print("• APNs Key → Works for both environments")

def provide_immediate_workaround():
    """Provide immediate workaround options"""
    print("\n⚡ IMMEDIATE WORKAROUNDS")
    print("=" * 60)
    
    print("WORKAROUND 1: Use Firebase Console for now")
    print("• Firebase Console messaging works")
    print("• Use it for urgent notifications")
    print("• Fix backend APNs config separately")
    print()
    
    print("WORKAROUND 2: Switch to APNs Key")
    print("• APNs keys are more reliable than certificates")
    print("• Work for both development and production")
    print("• Less environment-specific issues")
    print()
    
    print("WORKAROUND 3: Check iOS app build configuration")
    print("• Ensure iOS app is built for correct environment")
    print("• Development build = development APNs")
    print("• Production build = production APNs")

def main():
    """Main function"""
    print("=" * 60)
    print("🔍 APNs ENVIRONMENT CHECKER")
    print("=" * 60)
    print("Checking for APNs environment mismatches that cause")
    print("InvalidProviderToken errors despite correct service account.")
    print("=" * 60)
    
    # Initialize Firebase
    try:
        firebase_admin.get_app()
    except ValueError:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
    
    # Check APNs environment
    check_apns_environment_mismatch()
    
    # Test with different APNs configurations
    test_with_explicit_apns_config()
    
    # Provide fix steps
    provide_apns_fix_steps()
    
    # Provide workarounds
    provide_immediate_workaround()
    
    print(f"\n" + "=" * 60)
    print("🎯 KEY INSIGHT:")
    print("Your service account is fine! The issue is APNs configuration.")
    print("Firebase Console works because it uses different APNs auth.")
    print("Fix the APNs certificates/keys in Firebase Console!")
    print("=" * 60)

if __name__ == "__main__":
    main()
