#!/usr/bin/env python3
"""
Individual FCM Token Testing Script

This script tests individual FCM tokens with actual notification delivery
to identify which specific tokens are failing and why.

Usage:
    cd backend
    python3 test_individual_tokens.py
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path
from datetime import datetime

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
except ImportError:
    print("⚠️ python-dotenv not available")

import firebase_admin
from firebase_admin import messaging
import database.notifications as notification_db
from database._client import db
from utils.notifications import send_notification

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        firebase_admin.get_app()
        print("✓ Firebase already initialized")
        return True
    except ValueError:
        pass
    
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print("✓ Firebase initialized with service account JSON")
            return True
        else:
            firebase_admin.initialize_app()
            print("✓ Firebase initialized with default credentials")
            return True
    except Exception as e:
        print(f"❌ Failed to initialize Firebase: {e}")
        return False

def get_all_user_tokens_with_details():
    """Get all FCM tokens with user details from the database"""
    try:
        print("🔍 Retrieving FCM tokens with user details...")
        users_ref = db.collection('users')
        tokens_data = []
        
        for doc in users_ref.stream():
            user_data = doc.to_dict()
            if 'fcm_token' in user_data and user_data['fcm_token']:
                tokens_data.append({
                    'uid': doc.id,
                    'token': user_data['fcm_token'],
                    'time_zone': user_data.get('time_zone', 'Unknown'),
                    'email': user_data.get('email', 'Unknown'),
                    'created_at': user_data.get('created_at', 'Unknown')
                })
        
        print(f"📱 Found {len(tokens_data)} users with FCM tokens")
        return tokens_data
    
    except Exception as e:
        print(f"❌ Error retrieving FCM tokens: {e}")
        return []

def test_individual_token_delivery(token_data, test_number):
    """Test actual notification delivery to a single token"""
    uid = token_data['uid']
    token = token_data['token']
    email = token_data.get('email', 'Unknown')
    
    print(f"\n🧪 TEST {test_number}: UID {uid[:8]}... ({email})")
    print("-" * 50)
    
    # Create unique test message
    timestamp = datetime.now().strftime("%H:%M:%S")
    title = f"Individual Test {test_number}"
    body = f"Testing token for {uid[:8]}... at {timestamp}"
    
    print(f"📤 Sending: {title}")
    print(f"   To: {uid[:8]}... ({email})")
    print(f"   Token: {token[:30]}...")
    
    try:
        # Use the actual send_notification function
        success = send_notification(
            token=token,
            title=title,
            body=body,
            data={
                "test_id": str(test_number),
                "uid": uid,
                "timestamp": timestamp
            }
        )
        
        if success:
            print(f"✅ Notification sent successfully")
            return True, "Success"
        else:
            print(f"❌ Notification failed to send")
            return False, "Send failed"
            
    except Exception as e:
        print(f"❌ Exception during send: {e}")
        return False, str(e)

def test_direct_firebase_messaging(token_data, test_number):
    """Test direct Firebase messaging without our wrapper"""
    uid = token_data['uid']
    token = token_data['token']
    
    print(f"\n🔥 DIRECT FIREBASE TEST {test_number}: UID {uid[:8]}...")
    print("-" * 50)
    
    timestamp = datetime.now().strftime("%H:%M:%S")
    
    try:
        # Direct Firebase messaging
        notification = messaging.Notification(
            title=f"Direct Firebase Test {test_number}",
            body=f"Direct test for {uid[:8]}... at {timestamp}"
        )
        
        message = messaging.Message(
            notification=notification,
            token=token,
            data={
                "test_type": "direct_firebase",
                "test_id": str(test_number),
                "timestamp": timestamp
            }
        )
        
        response = messaging.send(message)
        print(f"✅ Direct Firebase send successful: {response}")
        return True, response
        
    except Exception as e:
        print(f"❌ Direct Firebase send failed: {e}")
        return False, str(e)

def analyze_token_patterns(tokens_data):
    """Analyze patterns in the tokens to identify potential issues"""
    print(f"\n🔍 ANALYZING TOKEN PATTERNS")
    print("=" * 60)
    
    # Group tokens by characteristics
    token_lengths = {}
    token_prefixes = {}
    
    for token_data in tokens_data:
        token = token_data['token']
        
        # Analyze token length
        length = len(token)
        token_lengths[length] = token_lengths.get(length, 0) + 1
        
        # Analyze token prefix (first 10 characters)
        prefix = token[:10]
        token_prefixes[prefix] = token_prefixes.get(prefix, 0) + 1
    
    print(f"Token length distribution:")
    for length, count in sorted(token_lengths.items()):
        print(f"  {length} characters: {count} tokens")
    
    print(f"\nToken prefix analysis:")
    for prefix, count in sorted(token_prefixes.items()):
        print(f"  {prefix}...: {count} tokens")
    
    # Check for suspicious patterns
    if len(token_lengths) > 1:
        print(f"\n⚠️ Multiple token lengths detected - may indicate mixed token types")
    
    if len(token_prefixes) == 1:
        print(f"\n⚠️ All tokens have same prefix - may indicate same device/app instance")

def provide_apns_debugging_steps():
    """Provide APNs-specific debugging steps"""
    print(f"\n🔧 APNs CONFIGURATION DEBUGGING")
    print("=" * 60)
    
    print("1. FIREBASE CONSOLE CHECKS:")
    print("   • Go to Firebase Console > Project Settings > Cloud Messaging")
    print("   • Verify APNs authentication is configured for iOS apps")
    print("   • Check if APNs keys/certificates are uploaded")
    print("   • Ensure Bundle IDs match exactly: me.memorion and me.memorion.dev")
    print()
    
    print("2. APNs CERTIFICATE/KEY VERIFICATION:")
    print("   • Check certificate expiration dates")
    print("   • Verify certificates are for correct Bundle IDs")
    print("   • Ensure production vs development environment matches")
    print("   • Test with both development and production certificates")
    print()
    
    print("3. iOS APP VERIFICATION:")
    print("   • Check app is signed with correct provisioning profile")
    print("   • Verify Push Notifications capability is enabled")
    print("   • Ensure Background App Refresh is enabled")
    print("   • Check notification permissions are granted")
    print()
    
    print("4. DEVICE/SIMULATOR CHECKS:")
    print("   • Physical device: Check network connectivity")
    print("   • Simulator: APNs may not work in simulator")
    print("   • Check device logs for APNs connection errors")
    print("   • Verify device time/timezone is correct")

def main():
    """Main function"""
    print("=" * 60)
    print("🧪 INDIVIDUAL FCM TOKEN TESTING")
    print("=" * 60)
    print("Testing each FCM token individually to identify")
    print("which specific tokens are failing and why.")
    print("=" * 60)
    
    # Initialize Firebase
    if not initialize_firebase():
        print("❌ Cannot proceed without Firebase initialization")
        sys.exit(1)
    
    # Get all tokens
    tokens_data = get_all_user_tokens_with_details()
    
    if not tokens_data:
        print("❌ No FCM tokens found in database")
        return
    
    # Analyze token patterns
    analyze_token_patterns(tokens_data)
    
    # Test each token individually
    print(f"\n🧪 TESTING {len(tokens_data)} TOKENS INDIVIDUALLY")
    print("=" * 60)
    
    successful_sends = 0
    failed_sends = 0
    
    for i, token_data in enumerate(tokens_data, 1):
        # Test with our wrapper function
        success1, result1 = test_individual_token_delivery(token_data, i)
        
        # Small delay between tests
        time.sleep(1)
        
        # Test with direct Firebase messaging
        success2, result2 = test_direct_firebase_messaging(token_data, i)
        
        if success1 and success2:
            successful_sends += 1
        else:
            failed_sends += 1
        
        # Delay between different tokens
        if i < len(tokens_data):
            print(f"\n⏳ Waiting 2 seconds before next token test...")
            time.sleep(2)
    
    # Summary
    print(f"\n📊 INDIVIDUAL TOKEN TEST RESULTS")
    print("=" * 60)
    print(f"Total tokens tested: {len(tokens_data)}")
    print(f"Successful sends: {successful_sends}")
    print(f"Failed sends: {failed_sends}")
    
    if failed_sends > 0:
        print(f"\n❌ {failed_sends} tokens failed - likely APNs configuration issue")
        provide_apns_debugging_steps()
    else:
        print(f"\n✅ All tokens sent successfully")
        print("   If you're not receiving notifications, check:")
        print("   • iOS app notification permissions")
        print("   • Device notification settings")
        print("   • APNs delivery delays")
    
    print(f"\n💡 NEXT STEPS:")
    print("   1. Check your iOS device for notifications")
    print("   2. If no notifications received, check Firebase Console APNs config")
    print("   3. Verify iOS app Bundle ID matches Firebase project")
    print("   4. Test on physical device (simulator APNs may not work)")

if __name__ == "__main__":
    main()
