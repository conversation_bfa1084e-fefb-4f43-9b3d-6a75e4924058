import asyncio
import math

from firebase_admin import messaging

import database.notifications as notification_db


def send_notification(token: str, title: str, body: str, data: dict = None):
    print('send_notification')
    notification = messaging.Notification(title=title, body=body)
    message = messaging.Message(notification=notification, token=token)

    if data:
        message.data = data

    try:
        response = messaging.send(message)
        print('send_notification success:', response)
        return True
    except Exception as e:
        error_message = str(e).lower()

        # Handle different types of FCM errors
        if "requested entity was not found" in error_message:
            print(f'send_notification failed: Token not found (app likely uninstalled) - removing token: {token[:20]}...')
            notification_db.remove_token(token)
        elif "invalid-registration-token" in error_message or "not a valid fcm registration token" in error_message:
            print(f'send_notification failed: Invalid FCM token format - removing token: {token[:20]}...')
            notification_db.remove_token(token)
        elif "registration-token-not-registered" in error_message:
            print(f'send_notification failed: Token not registered (app uninstalled/token expired) - removing token: {token[:20]}...')
            notification_db.remove_token(token)
        elif "sender-id-mismatch" in error_message:
            print(f'send_notification failed: Token registered with different sender ID - removing token: {token[:20]}...')
            notification_db.remove_token(token)
        elif "authentication" in error_message or "auth" in error_message:
            print(f'send_notification failed: Authentication error with Firebase - check service account credentials: {e}')
        elif "project" in error_message:
            print(f'send_notification failed: Firebase project configuration error: {e}')
        elif "quota-exceeded" in error_message:
            print(f'send_notification failed: FCM quota exceeded - rate limiting in effect: {e}')
        elif "unavailable" in error_message:
            print(f'send_notification failed: FCM service temporarily unavailable: {e}')
        else:
            print(f'send_notification failed: Unknown error: {e}')
            # Note: The original error message "Auth error from APNS or Web Push Service"
            # was misleading - this provides more specific error information
        return False


async def send_bulk_notification(user_tokens: list, title: str, body: str):
    try:
        batch_size = 500
        num_batches = math.ceil(len(user_tokens) / batch_size)

        def send_batch(batch_users):
            messages = [
                messaging.Message(notification=messaging.Notification(title=title, body=body), token=token)
                for token in batch_users
            ]
            return messaging.send_each(messages)

        tasks = []
        for i in range(num_batches):
            start = i * batch_size
            end = start + batch_size
            batch_users = user_tokens[start:end]
            task = asyncio.to_thread(send_batch, batch_users)
            tasks.append(task)

        await asyncio.gather(*tasks)

    except Exception as e:
        print("Error sending message:", e)
