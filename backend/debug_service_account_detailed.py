#!/usr/bin/env python3
"""
Detailed Service Account Debugging <PERSON>ript

This script performs deep analysis of service account authentication
when the service account has correct permissions but FCM still fails.

Usage:
    python3 debug_service_account_detailed.py
"""

import json
import os
import sys
from pathlib import Path
from datetime import datetime

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
except ImportError:
    print("⚠️ python-dotenv not available")

import firebase_admin
from firebase_admin import messaging
import requests

def initialize_firebase_with_debug():
    """Initialize Firebase with detailed debugging"""
    print("\n🔍 DETAILED FIREBASE INITIALIZATION")
    print("=" * 60)
    
    try:
        # Check if already initialized
        try:
            app = firebase_admin.get_app()
            print("✓ Firebase already initialized")
            print(f"  App name: {app.name}")
            print(f"  Project ID: {app.project_id}")
            return True
        except ValueError:
            print("🔄 Initializing Firebase...")
    
        if not os.environ.get('SERVICE_ACCOUNT_JSON'):
            print("❌ SERVICE_ACCOUNT_JSON not found")
            return False
        
        service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
        
        # Validate service account structure
        required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email']
        missing_fields = [field for field in required_fields if field not in service_account_info]
        
        if missing_fields:
            print(f"❌ Missing required fields: {missing_fields}")
            return False
        
        print(f"✓ Service account structure valid")
        print(f"  Email: {service_account_info['client_email']}")
        print(f"  Project: {service_account_info['project_id']}")
        
        # Initialize with detailed error handling
        credentials = firebase_admin.credentials.Certificate(service_account_info)
        app = firebase_admin.initialize_app(credentials)
        
        print(f"✅ Firebase initialized successfully")
        print(f"  App project ID: {app.project_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Firebase initialization failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def test_oauth_token_generation():
    """Test OAuth token generation for the service account"""
    print("\n🔍 TESTING OAUTH TOKEN GENERATION")
    print("=" * 60)
    
    try:
        if not os.environ.get('SERVICE_ACCOUNT_JSON'):
            print("❌ SERVICE_ACCOUNT_JSON not found")
            return False
        
        service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
        
        # Test token generation manually
        from google.auth import jwt
        from google.auth.transport.requests import Request
        
        # Create credentials
        credentials = firebase_admin.credentials.Certificate(service_account_info)
        
        # Get the underlying Google credentials
        google_creds = credentials.get_credential()
        
        # Refresh to get access token
        request = Request()
        google_creds.refresh(request)
        
        print(f"✅ OAuth token generated successfully")
        print(f"  Token expires: {google_creds.expiry}")
        print(f"  Token scope: {google_creds.scopes}")
        
        # Test if token has FCM scope
        if google_creds.scopes:
            fcm_scopes = [scope for scope in google_creds.scopes if 'firebase' in scope or 'messaging' in scope]
            if fcm_scopes:
                print(f"✅ FCM-related scopes found: {fcm_scopes}")
            else:
                print(f"⚠️ No FCM-specific scopes found")
                print(f"  All scopes: {google_creds.scopes}")
        
        return True
        
    except Exception as e:
        print(f"❌ OAuth token generation failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False

def test_fcm_api_directly():
    """Test FCM API directly with HTTP requests"""
    print("\n🔍 TESTING FCM API DIRECTLY")
    print("=" * 60)
    
    try:
        if not os.environ.get('SERVICE_ACCOUNT_JSON'):
            print("❌ SERVICE_ACCOUNT_JSON not found")
            return False
        
        service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
        project_id = service_account_info['project_id']
        
        # Get access token
        credentials = firebase_admin.credentials.Certificate(service_account_info)
        google_creds = credentials.get_credential()
        
        from google.auth.transport.requests import Request
        request = Request()
        google_creds.refresh(request)
        
        access_token = google_creds.token
        
        # Test FCM API endpoint
        fcm_url = f"https://fcm.googleapis.com/v1/projects/{project_id}/messages:send"
        
        # Get a test token
        test_token = "cWixBO7OeEdakAgoUImAXE:APA91bHSeTeQegPJzHxceH5Qpv8GlJlyZ1GCTRW8xYfHHfhLbrDnZ4hBtuxC5hQHU8jV0CVQLKiiV1C8l_DWeqo2IPfZtK199cgAMrSBzw18QJX38hkK000"
        
        payload = {
            "message": {
                "token": test_token,
                "notification": {
                    "title": "Direct API Test",
                    "body": "Testing FCM API directly"
                }
            }
        }
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        print(f"📤 Making direct FCM API request...")
        print(f"  URL: {fcm_url}")
        print(f"  Token: {test_token[:30]}...")
        
        response = requests.post(fcm_url, json=payload, headers=headers)
        
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"✅ Direct FCM API call successful!")
            print(f"  Response: {response.json()}")
            return True
        else:
            print(f"❌ Direct FCM API call failed")
            print(f"  Response: {response.text}")
            
            # Analyze specific error
            if response.status_code == 401:
                print(f"🔍 401 Unauthorized - Token/permissions issue")
            elif response.status_code == 403:
                print(f"🔍 403 Forbidden - Service account lacks permissions")
            elif response.status_code == 400:
                print(f"🔍 400 Bad Request - Message format issue")
            
            return False
        
    except Exception as e:
        print(f"❌ Direct FCM API test failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def test_different_message_formats():
    """Test different message formats to isolate the issue"""
    print("\n🔍 TESTING DIFFERENT MESSAGE FORMATS")
    print("=" * 60)
    
    test_token = "cWixBO7OeEdakAgoUImAXE:APA91bHSeTeQegPJzHxceH5Qpv8GlJlyZ1GCTRW8xYfHHfhLbrDnZ4hBtuxC5hQHU8jV0CVQLKiiV1C8l_DWeqo2IPfZtK199cgAMrSBzw18QJX38hkK000"
    
    test_cases = [
        {
            "name": "Minimal notification",
            "message": messaging.Message(
                notification=messaging.Notification(title="Test", body="Test"),
                token=test_token
            )
        },
        {
            "name": "Data only",
            "message": messaging.Message(
                data={"title": "Test", "body": "Test"},
                token=test_token
            )
        },
        {
            "name": "With APNs config",
            "message": messaging.Message(
                notification=messaging.Notification(title="Test", body="Test"),
                apns=messaging.APNSConfig(
                    payload=messaging.APNSPayload(
                        aps=messaging.Aps(alert="Test")
                    )
                ),
                token=test_token
            )
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        
        try:
            # Test with dry run first
            print(f"   Dry run...")
            dry_response = messaging.send(test_case['message'], dry_run=True)
            print(f"   ✅ Dry run success: {dry_response}")
            
            # Test actual send
            print(f"   Actual send...")
            response = messaging.send(test_case['message'], dry_run=False)
            print(f"   ✅ Actual send success: {response}")
            
            results.append((test_case['name'], True, response))
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            print(f"   Error type: {type(e).__name__}")
            results.append((test_case['name'], False, str(e)))
    
    return results

def analyze_error_patterns():
    """Analyze error patterns to identify root cause"""
    print("\n🔍 ERROR PATTERN ANALYSIS")
    print("=" * 60)
    
    print("POSSIBLE CAUSES FOR 'Auth error from APNS or Web Push Service':")
    print()
    
    print("1. SERVICE ACCOUNT ISSUES:")
    print("   • Service account key expired or invalid")
    print("   • Service account deleted or disabled")
    print("   • Project ID mismatch")
    print("   • Private key corruption")
    print()
    
    print("2. FIREBASE PROJECT ISSUES:")
    print("   • FCM API not enabled for project")
    print("   • Project billing issues")
    print("   • Project suspended or restricted")
    print()
    
    print("3. APNs CONFIGURATION ISSUES:")
    print("   • APNs certificates expired")
    print("   • Bundle ID mismatch in APNs config")
    print("   • APNs key/certificate not uploaded")
    print("   • Wrong APNs environment (dev vs prod)")
    print()
    
    print("4. TOKEN ISSUES:")
    print("   • FCM tokens generated with different project")
    print("   • Tokens from old Bundle ID still in database")
    print("   • Token format corruption")

def main():
    """Main function"""
    print("=" * 60)
    print("🔍 DETAILED SERVICE ACCOUNT DEBUGGING")
    print("=" * 60)
    print("Deep analysis of service account authentication issues")
    print("when permissions appear correct but FCM still fails.")
    print("=" * 60)
    
    # Initialize Firebase with debugging
    if not initialize_firebase_with_debug():
        print("❌ Cannot proceed without Firebase initialization")
        sys.exit(1)
    
    # Test OAuth token generation
    oauth_success = test_oauth_token_generation()
    
    # Test FCM API directly
    api_success = test_fcm_api_directly()
    
    # Test different message formats
    if not api_success:
        print("\n⚠️ Skipping message format tests due to API failure")
        message_results = []
    else:
        message_results = test_different_message_formats()
    
    # Analyze error patterns
    analyze_error_patterns()
    
    # Summary
    print(f"\n📊 DEBUGGING SUMMARY")
    print("=" * 60)
    print(f"OAuth Token Generation: {'✅ Success' if oauth_success else '❌ Failed'}")
    print(f"Direct FCM API Call: {'✅ Success' if api_success else '❌ Failed'}")
    
    if message_results:
        successful_formats = [r for r in message_results if r[1]]
        print(f"Message Format Tests: {len(successful_formats)}/{len(message_results)} successful")
    
    print(f"\n🎯 NEXT STEPS:")
    if not oauth_success:
        print("• Fix OAuth token generation issues")
        print("• Check service account key validity")
    elif not api_success:
        print("• Check FCM API permissions")
        print("• Verify project configuration")
        print("• Check APNs configuration in Firebase Console")
    else:
        print("• All tests passed - issue may be intermittent")
        print("• Check for rate limiting or quota issues")

if __name__ == "__main__":
    main()
