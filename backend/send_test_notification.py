#!/usr/bin/env python3
"""
Test Notification Script

Sends a test notification to all users in the database to verify
that the notification system is working correctly on mobile devices.

Usage:
    cd backend
    python3 send_test_notification.py
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
    else:
        print(f"⚠️ .env file not found at {env_path}")
except ImportError:
    print("⚠️ python-dotenv not available, using system environment variables")

import firebase_admin
from utils.notifications import send_bulk_notification
from database._client import db

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        # Check if Firebase is already initialized
        firebase_admin.get_app()
        print("✓ Firebase already initialized")
        return True
    except ValueError:
        # Firebase not initialized, initialize it
        print("🔄 Initializing Firebase...")
    
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print("✓ Firebase initialized with service account JSON")
            return True
        else:
            # Try to initialize with default credentials
            firebase_admin.initialize_app()
            print("✓ Firebase initialized with default credentials")
            return True
    except Exception as e:
        print(f"❌ Failed to initialize Firebase: {e}")
        return False

def get_all_user_tokens():
    """Get all FCM tokens from the database"""
    try:
        print("🔍 Retrieving FCM tokens from database...")
        users_ref = db.collection('users')
        tokens = []
        user_count = 0
        
        for doc in users_ref.stream():
            user_count += 1
            user_data = doc.to_dict()
            if 'fcm_token' in user_data and user_data['fcm_token']:
                tokens.append(user_data['fcm_token'])
        
        print(f"📊 Found {user_count} total users in database")
        print(f"📱 Found {len(tokens)} users with FCM tokens")
        return tokens
    
    except Exception as e:
        print(f"❌ Error retrieving FCM tokens: {e}")
        return []

async def send_test_notification_to_all():
    """Send test notification to all users"""
    # Get current timestamp for the notification
    current_time = datetime.now()
    formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # Create notification content
    title = "🧪 Test Notification"
    body = f"This is a test notification sent at {formatted_time}. If you receive this, your notification system is working correctly! 🎉"
    
    print(f"\n📤 Preparing to send test notification:")
    print(f"   Title: {title}")
    print(f"   Body: {body[:50]}...")
    print(f"   Timestamp: {formatted_time}")
    
    # Get all user tokens
    tokens = get_all_user_tokens()
    
    if not tokens:
        print("❌ No FCM tokens found. Cannot send notifications.")
        return False
    
    try:
        print(f"\n🚀 Sending notification to {len(tokens)} users...")
        
        # Use the existing send_bulk_notification function
        await send_bulk_notification(
            user_tokens=tokens,
            title=title,
            body=body
        )
        
        print(f"✅ Test notification sent successfully to {len(tokens)} users!")
        print(f"📱 Check your mobile device for the notification.")
        print(f"⏰ Sent at: {formatted_time}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to send test notification: {e}")
        import traceback
        print("📋 Error details:")
        print(traceback.format_exc())
        return False

def main():
    """Main function"""
    print("=" * 60)
    print("🧪 NOTIFICATION SYSTEM TEST")
    print("=" * 60)
    print("This script will send a test notification to all users")
    print("in the database to verify the notification system is working.")
    print("=" * 60)
    
    # Initialize Firebase
    if not initialize_firebase():
        print("❌ Cannot proceed without Firebase initialization")
        sys.exit(1)
    
    # Confirm before sending
    print(f"\n⚠️  This will send a test notification to ALL users in the database.")
    try:
        confirm = input("Do you want to continue? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ Test cancelled by user")
            sys.exit(0)
    except KeyboardInterrupt:
        print("\n❌ Test cancelled by user")
        sys.exit(0)
    
    # Send the test notification
    print(f"\n🔄 Starting notification test...")
    
    try:
        success = asyncio.run(send_test_notification_to_all())
        
        if success:
            print("\n" + "=" * 60)
            print("✅ NOTIFICATION TEST COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print("📱 Check your mobile device for the test notification.")
            print("🕐 The notification should appear within a few seconds.")
            print("💡 If you don't receive it, check:")
            print("   • App notification permissions are enabled")
            print("   • App is properly connected to Firebase")
            print("   • Device has internet connection")
        else:
            print("\n" + "=" * 60)
            print("❌ NOTIFICATION TEST FAILED")
            print("=" * 60)
            print("🔍 Check the error messages above for details.")
            
    except KeyboardInterrupt:
        print("\n❌ Test interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error during test: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
