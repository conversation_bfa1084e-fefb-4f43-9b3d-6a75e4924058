#!/usr/bin/env python3
"""
Test the complete notification flow to identify authentication issues
"""

import asyncio
import json
import os
import sys
import logging
from pathlib import Path
from datetime import datetime
import pytz

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
except ImportError:
    print("Warning: python-dotenv not available")

import firebase_admin
from firebase_admin import messaging
import database.notifications as notification_db
from database._client import db
from utils.notifications import send_notification, send_bulk_notification

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        firebase_admin.get_app()
        return True
    except ValueError:
        pass
    
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            return True
        else:
            firebase_admin.initialize_app()
            return True
    except Exception as e:
        print(f"Failed to initialize Firebase: {e}")
        return False

def get_sample_tokens():
    """Get a few sample FCM tokens for testing"""
    try:
        users_ref = db.collection('users')
        tokens = []
        
        count = 0
        for doc in users_ref.stream():
            if count >= 3:  # Only get first 3 tokens for testing
                break
                
            user_data = doc.to_dict()
            if 'fcm_token' in user_data:
                tokens.append({
                    'uid': doc.id,
                    'token': user_data['fcm_token'],
                    'time_zone': user_data.get('time_zone', 'Unknown')
                })
                count += 1
        
        return tokens
    except Exception as e:
        print(f"Error getting sample tokens: {e}")
        return []

def test_single_notification(token_data):
    """Test sending a single notification"""
    logger = logging.getLogger(__name__)
    
    token = token_data['token']
    uid = token_data['uid']
    
    logger.info(f"Testing single notification to UID: {uid[:8]}...")
    
    try:
        # Use the actual send_notification function from utils
        send_notification(
            token=token,
            title="Test Notification",
            body="Testing notification authentication",
            data={"test": "true", "timestamp": str(datetime.now())}
        )
        logger.info("✅ Single notification test completed")
        return True
    except Exception as e:
        logger.error(f"❌ Single notification test failed: {e}")
        return False

async def test_bulk_notification(tokens_data):
    """Test sending bulk notifications"""
    logger = logging.getLogger(__name__)
    
    tokens = [data['token'] for data in tokens_data]
    logger.info(f"Testing bulk notification to {len(tokens)} tokens...")
    
    try:
        # Use the actual send_bulk_notification function from utils
        await send_bulk_notification(
            user_tokens=tokens,
            title="Bulk Test Notification",
            body="Testing bulk notification authentication"
        )
        logger.info("✅ Bulk notification test completed")
        return True
    except Exception as e:
        logger.error(f"❌ Bulk notification test failed: {e}")
        return False

async def test_timezone_notification():
    """Test the timezone-based notification system"""
    logger = logging.getLogger(__name__)
    
    logger.info("Testing timezone-based notification system...")
    
    try:
        # Import the notification functions
        from utils.other.notifications import _get_timezones_at_time, _get_users_in_timezone
        
        # Get current time in HH:MM format
        current_time = datetime.now(pytz.utc).strftime("%H:%M")
        logger.info(f"Current UTC time: {current_time}")
        
        # Get timezones at current time
        timezones = _get_timezones_at_time(current_time)
        logger.info(f"Found {len(timezones)} timezones at current time")
        
        if timezones:
            # Get users in these timezones
            users = await _get_users_in_timezone(current_time)
            logger.info(f"Found {len(users) if users else 0} users in current time zones")
            
            if users:
                # Test sending to these users
                await send_bulk_notification(
                    user_tokens=users,
                    title="Timezone Test Notification",
                    body="Testing timezone-based notification system"
                )
                logger.info("✅ Timezone notification test completed")
            else:
                logger.info("ℹ️ No users found in current timezone - this is normal")
        else:
            logger.info("ℹ️ No timezones match current time - this is normal")
        
        return True
    except Exception as e:
        logger.error(f"❌ Timezone notification test failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_firebase_messaging_directly():
    """Test Firebase messaging directly without our wrapper functions"""
    logger = logging.getLogger(__name__)
    
    logger.info("Testing Firebase messaging directly...")
    
    try:
        # Get a sample token
        tokens_data = get_sample_tokens()
        if not tokens_data:
            logger.warning("No tokens available for direct Firebase test")
            return True
        
        token = tokens_data[0]['token']
        
        # Test direct Firebase messaging
        notification = messaging.Notification(
            title="Direct Firebase Test",
            body="Testing Firebase messaging directly"
        )
        message = messaging.Message(
            notification=notification,
            token=token,
            data={"test": "direct_firebase", "timestamp": str(datetime.now())}
        )
        
        response = messaging.send(message)
        logger.info(f"✅ Direct Firebase test successful: {response}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Direct Firebase test failed: {e}")
        return False

async def main():
    """Main function to run all notification tests"""
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("🔍 COMPLETE NOTIFICATION FLOW TEST")
    logger.info("=" * 60)
    
    # Initialize Firebase
    if not initialize_firebase():
        logger.error("Failed to initialize Firebase")
        sys.exit(1)
    
    # Get sample tokens
    logger.info("Getting sample FCM tokens...")
    tokens_data = get_sample_tokens()
    
    if not tokens_data:
        logger.warning("No FCM tokens found - cannot test notifications")
        return
    
    logger.info(f"Found {len(tokens_data)} tokens for testing")
    
    # Test 1: Direct Firebase messaging
    logger.info("\n1. Testing direct Firebase messaging...")
    test_firebase_messaging_directly()
    
    # Test 2: Single notification
    logger.info("\n2. Testing single notification...")
    test_single_notification(tokens_data[0])
    
    # Test 3: Bulk notification
    logger.info("\n3. Testing bulk notification...")
    await test_bulk_notification(tokens_data)
    
    # Test 4: Timezone-based notification
    logger.info("\n4. Testing timezone-based notification...")
    await test_timezone_notification()
    
    logger.info("\n" + "=" * 60)
    logger.info("✅ NOTIFICATION FLOW TESTS COMPLETED")
    logger.info("=" * 60)
    logger.info("If you see 'Auth error from APNS or Web Push Service' messages,")
    logger.info("they should now have more specific error details in the logs above.")

if __name__ == "__main__":
    asyncio.run(main())
