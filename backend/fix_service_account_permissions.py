#!/usr/bin/env python3
"""
Service Account Permissions Fix Script

This script helps identify and fix service account permission issues
that prevent FCM message sending.

Usage:
    python3 fix_service_account_permissions.py
"""

import json
import os
import sys
from pathlib import Path

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
except ImportError:
    print("⚠️ python-dotenv not available")

def analyze_current_service_account():
    """Analyze the current service account"""
    print("🔍 ANALYZING CURRENT SERVICE ACCOUNT")
    print("=" * 60)
    
    if not os.environ.get('SERVICE_ACCOUNT_JSON'):
        print("❌ SERVICE_ACCOUNT_JSON not found")
        return None
    
    try:
        service_account = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
        
        email = service_account.get('client_email', '')
        project_id = service_account.get('project_id', '')
        
        print(f"📧 Service Account Email: {email}")
        print(f"🏗️ Project ID: {project_id}")
        print(f"🔑 Private Key ID: {service_account.get('private_key_id', '')}")
        
        # Analyze the service account type
        if 'firebase-adminsdk' in email:
            print("✅ This is a Firebase Admin SDK service account")
            print("   Should have proper FCM permissions")
        elif 'iam.gserviceaccount.com' in email:
            print("⚠️ This is a custom Google Cloud service account")
            print("   May lack FCM sending permissions")
            print("   This is likely the cause of the issue!")
        else:
            print("❓ Unknown service account type")
        
        return service_account
        
    except Exception as e:
        print(f"❌ Error parsing service account: {e}")
        return None

def provide_service_account_fix_options():
    """Provide options to fix service account permissions"""
    print("\n🔧 SERVICE ACCOUNT FIX OPTIONS")
    print("=" * 60)
    
    print("OPTION 1: CREATE NEW FIREBASE ADMIN SDK SERVICE ACCOUNT (Recommended)")
    print("=" * 50)
    print("1. Go to Firebase Console:")
    print("   https://console.firebase.google.com/project/chat10000-402ac/settings/serviceaccounts/adminsdk")
    print()
    print("2. Click 'Generate new private key'")
    print("3. Download the JSON file")
    print("4. Replace SERVICE_ACCOUNT_JSON in .env with new content")
    print("5. This will have proper FCM permissions automatically")
    print()
    
    print("OPTION 2: ADD PERMISSIONS TO CURRENT SERVICE ACCOUNT")
    print("=" * 50)
    print("1. Go to Google Cloud Console IAM:")
    print("   https://console.cloud.google.com/iam-admin/iam?project=chat10000-402ac")
    print()
    print("2. Find service account: *******")
    print("3. Click 'Edit' (pencil icon)")
    print("4. Add these roles:")
    print("   • Firebase Admin SDK Administrator Service Agent")
    print("   • Firebase Cloud Messaging Admin")
    print("   • Cloud Messaging Admin")
    print("5. Save changes")
    print()
    
    print("OPTION 3: USE GOOGLE APPLICATION DEFAULT CREDENTIALS")
    print("=" * 50)
    print("1. Remove SERVICE_ACCOUNT_JSON from .env")
    print("2. Set up Application Default Credentials:")
    print("   gcloud auth application-default login")
    print("3. This uses your personal Google account with proper permissions")
    print("4. Good for development, not recommended for production")

def create_test_script_with_new_service_account():
    """Create a script to test with a new service account"""
    print("\n📝 TESTING NEW SERVICE ACCOUNT")
    print("=" * 60)
    
    test_script = '''#!/usr/bin/env python3
"""
Test script for new Firebase Admin SDK service account
"""

import json
import os
import firebase_admin
from firebase_admin import credentials, messaging

def test_new_service_account():
    # Replace this with your new Firebase Admin SDK service account JSON
    new_service_account_json = """
    {
        "type": "service_account",
        "project_id": "chat10000-402ac",
        "private_key_id": "YOUR_NEW_KEY_ID",
        "private_key": "-----BEGIN PRIVATE KEY-----\\nYOUR_NEW_PRIVATE_KEY\\n-----END PRIVATE KEY-----\\n",
        "client_email": "*******",
        "client_id": "YOUR_CLIENT_ID",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40chat10000-402ac.iam.gserviceaccount.com"
    }
    """
    
    try:
        service_account_info = json.loads(new_service_account_json)
        cred = credentials.Certificate(service_account_info)
        firebase_admin.initialize_app(cred)
        
        # Test with your FCM token
        token = "cWixBO7OeEdakAgoUImAXE:APA91bHSeTeQegPJzHxceH5Qpv8GlJlyZ1GCTRW8xYfHHfhLbrDnZ4hBtuxC5hQHU8jV0CVQLKiiV1C8l_DWeqo2IPfZtK199cgAMrSBzw18QJX38hkK000"
        
        message = messaging.Message(
            notification=messaging.Notification(
                title="New Service Account Test",
                body="Testing with Firebase Admin SDK service account"
            ),
            token=token
        )
        
        response = messaging.send(message)
        print(f"✅ Success with new service account: {response}")
        
    except Exception as e:
        print(f"❌ Failed with new service account: {e}")

if __name__ == "__main__":
    test_new_service_account()
'''
    
    with open('test_new_service_account.py', 'w') as f:
        f.write(test_script)
    
    print("Created test_new_service_account.py")
    print("Edit this file with your new Firebase Admin SDK service account JSON")

def provide_immediate_fix_steps():
    """Provide immediate steps to fix the issue"""
    print("\n🚀 IMMEDIATE FIX STEPS")
    print("=" * 60)
    
    print("STEP 1: Get Firebase Admin SDK Service Account")
    print("1. Go to: https://console.firebase.google.com/project/chat10000-402ac/settings/serviceaccounts/adminsdk")
    print("2. Click 'Generate new private key'")
    print("3. Download the JSON file")
    print()
    
    print("STEP 2: Replace Service Account in .env")
    print("1. Open backend/.env")
    print("2. Replace the entire SERVICE_ACCOUNT_JSON value with the new JSON")
    print("3. Make sure it's properly escaped (use online JSON escape tool if needed)")
    print()
    
    print("STEP 3: Test Immediately")
    print("1. Run: python3 debug_backend_vs_console.py")
    print("2. Should see ✅ Success instead of ❌ Auth error")
    print("3. Run: python3 send_test_notification.py")
    print("4. Should receive notifications on your device")
    print()
    
    print("🎯 WHY THIS WILL WORK:")
    print("• Firebase Admin SDK service accounts have built-in FCM permissions")
    print("• Firebase Console uses similar authentication internally")
    print("• Your current service account lacks FCM sending permissions")
    print("• This is a common issue when using custom service accounts")

def main():
    """Main function"""
    print("=" * 60)
    print("🔧 SERVICE ACCOUNT PERMISSIONS FIX")
    print("=" * 60)
    print("Fixing service account permissions for FCM message sending")
    print("=" * 60)
    
    # Analyze current service account
    service_account = analyze_current_service_account()
    
    # Provide fix options
    provide_service_account_fix_options()
    
    # Create test script
    create_test_script_with_new_service_account()
    
    # Provide immediate steps
    provide_immediate_fix_steps()
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY:")
    print("Your service account lacks FCM permissions.")
    print("Get a Firebase Admin SDK service account to fix this!")
    print("=" * 60)

if __name__ == "__main__":
    main()
