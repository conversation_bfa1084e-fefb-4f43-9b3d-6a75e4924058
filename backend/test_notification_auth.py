#!/usr/bin/env python3
"""
Test script to diagnose notification authentication issues
"""

import asyncio
import json
import os
import sys
import logging
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
    else:
        print(f"✗ .env file not found at {env_path}")
except ImportError:
    print("Warning: python-dotenv not available, using system environment variables")

import firebase_admin
from firebase_admin import messaging

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)

def test_firebase_initialization():
    """Test Firebase initialization"""
    logger = logging.getLogger(__name__)
    
    try:
        # Check if Firebase is already initialized
        app = firebase_admin.get_app()
        logger.info("✓ Firebase already initialized")
        return True
    except ValueError:
        # Firebase not initialized, initialize it
        logger.info("Firebase not initialized, attempting to initialize...")
    
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            logger.info("Found SERVICE_ACCOUNT_JSON environment variable")
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            
            # Validate service account structure
            required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email']
            missing_fields = [field for field in required_fields if field not in service_account_info]
            
            if missing_fields:
                logger.error(f"✗ Missing required fields in service account: {missing_fields}")
                return False
            
            logger.info(f"✓ Service account project_id: {service_account_info.get('project_id')}")
            logger.info(f"✓ Service account client_email: {service_account_info.get('client_email')}")
            
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            logger.info("✓ Firebase initialized with service account JSON")
            return True
        else:
            logger.warning("SERVICE_ACCOUNT_JSON not found, trying default credentials")
            # Try to initialize with default credentials
            firebase_admin.initialize_app()
            logger.info("✓ Firebase initialized with default credentials")
            return True
    except Exception as e:
        logger.error(f"✗ Failed to initialize Firebase: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        return False

def test_notification_send():
    """Test sending a notification to check for authentication errors"""
    logger = logging.getLogger(__name__)

    # Use a dummy FCM token for testing (this will fail but show auth status)
    dummy_token = "dummy_fcm_token_for_testing_auth"

    try:
        notification = messaging.Notification(
            title="Test Notification",
            body="Testing authentication"
        )
        message = messaging.Message(
            notification=notification,
            token=dummy_token
        )

        logger.info("Attempting to send test notification...")
        response = messaging.send(message)
        logger.info(f"✓ Notification sent successfully: {response}")
        return True

    except Exception as e:
        error_message = str(e)
        logger.error(f"✗ Notification send failed: {error_message}")

        # Analyze the error to determine if it's authentication-related
        if "auth" in error_message.lower() or "authentication" in error_message.lower():
            logger.error("🔥 AUTHENTICATION ERROR DETECTED!")
            logger.error("This indicates an issue with Firebase credentials or permissions")
        elif "invalid-registration-token" in error_message.lower() or "registration-token-not-registered" in error_message.lower():
            logger.info("✓ Authentication appears to be working (token error is expected with dummy token)")
            return True
        elif "not a valid FCM registration token" in error_message:
            logger.info("✓ Authentication appears to be working (invalid token error is expected with dummy token)")
            return True
        elif "project" in error_message.lower():
            logger.error("🔥 PROJECT CONFIGURATION ERROR!")
            logger.error("This indicates an issue with the Firebase project configuration")
        else:
            logger.error(f"🔥 UNKNOWN ERROR: {error_message}")

        return False

def analyze_firebase_errors():
    """Analyze common Firebase FCM errors and their meanings"""
    logger = logging.getLogger(__name__)

    logger.info("\n📋 COMMON FIREBASE FCM ERRORS AND THEIR MEANINGS:")
    logger.info("=" * 60)

    error_explanations = {
        "invalid-registration-token": "Token format is invalid or malformed",
        "registration-token-not-registered": "Token is not registered with FCM (app uninstalled/token expired)",
        "invalid-package-name": "Package name in token doesn't match project",
        "authentication-error": "Service account credentials are invalid",
        "project-not-found": "Firebase project ID is incorrect",
        "sender-id-mismatch": "Token was registered with different sender ID",
        "quota-exceeded": "Message rate quota exceeded",
        "unavailable": "FCM service temporarily unavailable",
        "internal-error": "Internal FCM server error"
    }

    for error_code, explanation in error_explanations.items():
        logger.info(f"• {error_code}: {explanation}")

    logger.info("=" * 60)

def check_firebase_project_permissions():
    """Check Firebase project and permissions"""
    logger = logging.getLogger(__name__)
    
    try:
        # Try to access Firebase project info
        app = firebase_admin.get_app()
        project_id = app.project_id
        logger.info(f"✓ Connected to Firebase project: {project_id}")
        
        # Check if we can access messaging service
        from firebase_admin import messaging
        logger.info("✓ Firebase Messaging service is accessible")
        
        return True
    except Exception as e:
        logger.error(f"✗ Firebase project access failed: {e}")
        return False

def main():
    """Main function to run all tests"""
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("🔍 NOTIFICATION AUTHENTICATION DIAGNOSTIC TEST")
    logger.info("=" * 60)
    
    # Test 1: Firebase Initialization
    logger.info("\n1. Testing Firebase Initialization...")
    if not test_firebase_initialization():
        logger.error("❌ Firebase initialization failed - cannot proceed")
        sys.exit(1)
    
    # Test 2: Firebase Project Permissions
    logger.info("\n2. Testing Firebase Project Permissions...")
    if not check_firebase_project_permissions():
        logger.error("❌ Firebase project access failed")
        sys.exit(1)
    
    # Test 3: Notification Send Test
    logger.info("\n3. Testing Notification Send (Authentication Check)...")
    if not test_notification_send():
        logger.error("❌ Notification authentication test failed")
        sys.exit(1)

    # Test 4: Show error explanations
    logger.info("\n4. Firebase Error Reference...")
    analyze_firebase_errors()

    logger.info("\n" + "=" * 60)
    logger.info("✅ ALL TESTS PASSED - Authentication appears to be working!")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
