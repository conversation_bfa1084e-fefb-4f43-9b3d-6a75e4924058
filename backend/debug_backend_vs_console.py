#!/usr/bin/env python3
"""
Backend vs Firebase Console Comparison Script

This script helps identify differences between our backend implementation
and Firebase Console's successful message sending.

Usage:
    python3 debug_backend_vs_console.py
"""

import json
import os
import sys
from pathlib import Path
from datetime import datetime

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
except ImportError:
    print("⚠️ python-dotenv not available")

import firebase_admin
from firebase_admin import messaging

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        firebase_admin.get_app()
        print("✓ Firebase already initialized")
        return True
    except ValueError:
        pass
    
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print("✓ Firebase initialized with service account JSON")
            return True
        else:
            firebase_admin.initialize_app()
            print("✓ Firebase initialized with default credentials")
            return True
    except Exception as e:
        print(f"❌ Failed to initialize Firebase: {e}")
        return False

def analyze_service_account():
    """Analyze our service account configuration"""
    print("\n🔍 ANALYZING SERVICE ACCOUNT CONFIGURATION")
    print("=" * 60)
    
    if not os.environ.get('SERVICE_ACCOUNT_JSON'):
        print("❌ SERVICE_ACCOUNT_JSON not found")
        return None
    
    try:
        service_account = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
        
        print(f"✓ Service Account Email: {service_account.get('client_email')}")
        print(f"✓ Project ID: {service_account.get('project_id')}")
        print(f"✓ Private Key ID: {service_account.get('private_key_id')}")
        print(f"✓ Type: {service_account.get('type')}")
        
        # Check if this is a Firebase Admin SDK service account
        email = service_account.get('client_email', '')
        if 'firebase-adminsdk' in email:
            print("✅ This is a Firebase Admin SDK service account")
        else:
            print("⚠️ This may not be a Firebase Admin SDK service account")
            print("   Firebase Console uses different authentication")
        
        return service_account
        
    except Exception as e:
        print(f"❌ Error parsing service account: {e}")
        return None

def test_minimal_message(token):
    """Test with the most minimal message possible"""
    print(f"\n🧪 TESTING MINIMAL MESSAGE")
    print("=" * 60)
    
    try:
        # Most basic message - exactly like Firebase Console
        message = messaging.Message(
            notification=messaging.Notification(
                title="Minimal Test",
                body="Testing minimal message"
            ),
            token=token
        )
        
        print("📤 Sending minimal message...")
        response = messaging.send(message)
        print(f"✅ Minimal message sent successfully: {response}")
        return True
        
    except Exception as e:
        print(f"❌ Minimal message failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False

def test_message_variations(token):
    """Test different message variations to isolate the issue"""
    print(f"\n🧪 TESTING MESSAGE VARIATIONS")
    print("=" * 60)
    
    variations = [
        {
            "name": "Notification only",
            "message": messaging.Message(
                notification=messaging.Notification(title="Test", body="Test"),
                token=token
            )
        },
        {
            "name": "With data payload",
            "message": messaging.Message(
                notification=messaging.Notification(title="Test", body="Test"),
                data={"test": "true"},
                token=token
            )
        },
        {
            "name": "Data only (no notification)",
            "message": messaging.Message(
                data={"test": "true", "title": "Test", "body": "Test"},
                token=token
            )
        },
        {
            "name": "With APNs config",
            "message": messaging.Message(
                notification=messaging.Notification(title="Test", body="Test"),
                apns=messaging.APNSConfig(
                    payload=messaging.APNSPayload(
                        aps=messaging.Aps(alert="Test", sound="default")
                    )
                ),
                token=token
            )
        }
    ]
    
    results = []
    
    for i, variation in enumerate(variations, 1):
        print(f"\n{i}. Testing: {variation['name']}")
        try:
            response = messaging.send(variation['message'])
            print(f"   ✅ Success: {response}")
            results.append((variation['name'], True, response))
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            results.append((variation['name'], False, str(e)))
    
    return results

def test_dry_run_vs_actual(token):
    """Test dry run vs actual sending"""
    print(f"\n🧪 TESTING DRY RUN VS ACTUAL SEND")
    print("=" * 60)
    
    message = messaging.Message(
        notification=messaging.Notification(title="Dry Run Test", body="Testing"),
        token=token
    )
    
    # Test dry run first
    print("1. Testing with dry_run=True...")
    try:
        response = messaging.send(message, dry_run=True)
        print(f"   ✅ Dry run successful: {response}")
        dry_run_success = True
    except Exception as e:
        print(f"   ❌ Dry run failed: {e}")
        dry_run_success = False
    
    # Test actual send
    print("2. Testing with dry_run=False...")
    try:
        response = messaging.send(message, dry_run=False)
        print(f"   ✅ Actual send successful: {response}")
        actual_success = True
    except Exception as e:
        print(f"   ❌ Actual send failed: {e}")
        actual_success = False
    
    return dry_run_success, actual_success

def compare_with_our_wrapper():
    """Compare direct Firebase SDK with our wrapper function"""
    print(f"\n🔍 COMPARING DIRECT SDK VS OUR WRAPPER")
    print("=" * 60)
    
    # Get a sample token
    try:
        from database._client import db
        users_ref = db.collection('users')
        token = None
        
        for doc in users_ref.stream():
            user_data = doc.to_dict()
            if 'fcm_token' in user_data and user_data['fcm_token']:
                token = user_data['fcm_token']
                break
        
        if not token:
            print("❌ No FCM token found for testing")
            return
        
        print(f"Using token: {token[:30]}...")
        
        # Test direct Firebase SDK
        print("\n1. Testing DIRECT Firebase SDK...")
        direct_success = test_minimal_message(token)
        
        # Test our wrapper function
        print("\n2. Testing OUR WRAPPER function...")
        try:
            from utils.notifications import send_notification
            wrapper_success = send_notification(
                token=token,
                title="Wrapper Test",
                body="Testing our wrapper function"
            )
            print(f"   Wrapper result: {wrapper_success}")
        except Exception as e:
            print(f"   ❌ Wrapper failed: {e}")
            wrapper_success = False
        
        # Compare results
        print(f"\n📊 COMPARISON RESULTS:")
        print(f"   Direct SDK: {'✅ Success' if direct_success else '❌ Failed'}")
        print(f"   Our Wrapper: {'✅ Success' if wrapper_success else '❌ Failed'}")
        
        if direct_success and not wrapper_success:
            print(f"\n🎯 ISSUE IDENTIFIED: Our wrapper function has a problem!")
            print(f"   The direct Firebase SDK works, but our wrapper fails.")
            print(f"   Check utils/notifications.py for issues.")
        elif not direct_success and not wrapper_success:
            print(f"\n🎯 ISSUE IDENTIFIED: Firebase SDK authentication problem!")
            print(f"   Both direct SDK and wrapper fail - service account issue.")
        elif direct_success and wrapper_success:
            print(f"\n🤔 MYSTERY: Both work in isolation!")
            print(f"   The issue might be in bulk sending or specific conditions.")
        
    except Exception as e:
        print(f"❌ Error in comparison: {e}")

def provide_next_steps():
    """Provide next steps based on findings"""
    print(f"\n🚀 NEXT STEPS BASED ON FINDINGS")
    print("=" * 60)
    
    print("1. IF DIRECT SDK WORKS BUT WRAPPER FAILS:")
    print("   • Check utils/notifications.py for bugs")
    print("   • Compare our message construction with working version")
    print("   • Look for error handling that might mask real issues")
    print()
    
    print("2. IF BOTH FAIL:")
    print("   • Service account permissions issue")
    print("   • Check if service account has FCM sending permissions")
    print("   • Verify service account is for correct project")
    print()
    
    print("3. IF BOTH WORK:")
    print("   • Issue is in bulk sending logic")
    print("   • Check send_bulk_notification function")
    print("   • Test with single vs multiple tokens")
    print()
    
    print("4. GENERAL DEBUGGING:")
    print("   • Enable more detailed logging")
    print("   • Test with different token types")
    print("   • Compare message payloads with Firebase Console")

def main():
    """Main function"""
    print("=" * 60)
    print("🔍 BACKEND VS FIREBASE CONSOLE COMPARISON")
    print("=" * 60)
    print("Since Firebase Console works, let's find what's different")
    print("in our backend implementation.")
    print("=" * 60)
    
    # Initialize Firebase
    if not initialize_firebase():
        print("❌ Cannot proceed without Firebase initialization")
        sys.exit(1)
    
    # Analyze service account
    service_account = analyze_service_account()
    
    # Compare implementations
    compare_with_our_wrapper()
    
    # Provide next steps
    provide_next_steps()
    
    print(f"\n" + "=" * 60)
    print("🎯 KEY INSIGHT:")
    print("Firebase Console works → APNs/certificates are correct")
    print("Our backend fails → Implementation or service account issue")
    print("Focus on fixing our backend code, not Firebase configuration!")
    print("=" * 60)

if __name__ == "__main__":
    main()
